<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Return Processing Logs - Real-time Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .stat-card p {
            color: #7f8c8d;
            font-weight: 500;
        }

        .stat-card.success h3 { color: #27ae60; }
        .stat-card.warning h3 { color: #f39c12; }
        .stat-card.error h3 { color: #e74c3c; }
        .stat-card.info h3 { color: #3498db; }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-info {
            background: #3498db;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }

        .logs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .logs-header {
            background: #2c3e50;
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .logs-content {
            max-height: 600px;
            overflow-y: auto;
            padding: 1rem;
        }

        .log-entry {
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .log-entry.success { border-left-color: #27ae60; }
        .log-entry.error { border-left-color: #e74c3c; }
        .log-entry.warning { border-left-color: #f39c12; }

        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
            margin-right: 1rem;
        }

        .log-level {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 1rem;
        }

        .log-level.info { background: #d1ecf1; color: #0c5460; }
        .log-level.success { background: #d4edda; color: #155724; }
        .log-level.error { background: #f8d7da; color: #721c24; }
        .log-level.warning { background: #fff3cd; color: #856404; }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: auto;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Return Processing Logs Dashboard</h1>
        <p>Real-time monitoring of Return Prime webhook processing and supplier notifications</p>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card success">
                <h3 id="total-processed">0</h3>
                <p>Total Returns Processed</p>
            </div>
            <div class="stat-card info">
                <h3 id="today-processed">0</h3>
                <p>Processed Today</p>
            </div>
            <div class="stat-card warning">
                <h3 id="pending-returns">0</h3>
                <p>Pending Returns</p>
            </div>
            <div class="stat-card error">
                <h3 id="failed-returns">0</h3>
                <p>Failed Returns</p>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshLogs()">
                🔄 Refresh Logs
            </button>
            <button class="btn btn-success" onclick="exportLogs()">
                📊 Export to Google Sheets
            </button>
            <button class="btn btn-info" onclick="clearLogs()">
                🗑️ Clear Display
            </button>
            <button class="btn" onclick="testReturn()" style="background: #9b59b6; color: white;">
                🧪 Test Return
            </button>
            <button class="btn" onclick="testError()" style="background: #e74c3c; color: white;">
                ⚠️ Test Error
            </button>
            
            <div class="auto-refresh">
                <span>Auto-refresh:</span>
                <label class="switch">
                    <input type="checkbox" id="auto-refresh-toggle" checked onchange="toggleAutoRefresh()">
                    <span class="slider"></span>
                </label>
            </div>

            <div style="margin-left: auto;">
                <span class="status-indicator status-online" id="connection-status"></span>
                <span id="connection-text">Connected</span>
            </div>
        </div>

        <!-- Logs Container -->
        <div class="logs-container">
            <div class="logs-header">
                <h2>📝 Live Processing Logs</h2>
                <span id="last-updated">Last updated: Never</span>
            </div>
            <div class="logs-content" id="logs-content">
                <div class="log-entry info">
                    <span class="log-timestamp">[2024-01-20 10:30:00]</span>
                    <span class="log-level info">INFO</span>
                    Dashboard initialized. Waiting for return processing events...
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshEnabled = true;
        let refreshInterval;
        const API_BASE = window.location.origin;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing logs dashboard...');
            startAutoRefresh();
            loadInitialData();
        });

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            
            if (autoRefreshEnabled) {
                refreshInterval = setInterval(() => {
                    refreshLogs();
                    updateStats();
                }, 5000); // Refresh every 5 seconds
            }
        }

        function toggleAutoRefresh() {
            autoRefreshEnabled = document.getElementById('auto-refresh-toggle').checked;
            startAutoRefresh();
            
            addLogEntry('info', autoRefreshEnabled ? 'Auto-refresh enabled' : 'Auto-refresh disabled');
        }

        async function loadInitialData() {
            try {
                await updateStats();
                await refreshLogs();
                updateConnectionStatus(true);
            } catch (error) {
                console.error('Failed to load initial data:', error);
                updateConnectionStatus(false);
                addLogEntry('error', `Failed to load initial data: ${error.message}`);
            }
        }

        async function updateStats() {
            try {
                const response = await fetch(`${API_BASE}/api/stats`);
                if (!response.ok) throw new Error('Failed to fetch stats');

                const data = await response.json();
                const stats = data.stats;

                document.getElementById('total-processed').textContent = stats.totalProcessed || 0;
                document.getElementById('today-processed').textContent = stats.todayProcessed || 0;
                document.getElementById('pending-returns').textContent = stats.pendingReturns || 0;
                document.getElementById('failed-returns').textContent = stats.failedReturns || 0;

            } catch (error) {
                console.error('Failed to update stats:', error);
                addLogEntry('error', `Failed to update statistics: ${error.message}`);

                // Fallback to default values
                document.getElementById('total-processed').textContent = '-';
                document.getElementById('today-processed').textContent = '-';
                document.getElementById('pending-returns').textContent = '-';
                document.getElementById('failed-returns').textContent = '-';
            }
        }

        async function refreshLogs() {
            try {
                const response = await fetch(`${API_BASE}/api/logs?limit=20`);
                if (!response.ok) throw new Error('Failed to fetch logs');

                const data = await response.json();
                const logs = data.logs;

                // Clear existing logs and add new ones
                const logsContent = document.getElementById('logs-content');
                logsContent.innerHTML = '';

                // Add fetched logs
                logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = `log-entry ${log.level}`;

                    const timestamp = new Date(log.timestamp).toLocaleString();
                    logEntry.innerHTML = `
                        <span class="log-timestamp">[${timestamp}]</span>
                        <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                        ${log.message}
                    `;

                    logsContent.appendChild(logEntry);
                });

                // If no logs, show placeholder
                if (logs.length === 0) {
                    logsContent.innerHTML = `
                        <div class="log-entry info">
                            <span class="log-timestamp">[${new Date().toLocaleString()}]</span>
                            <span class="log-level info">INFO</span>
                            No recent logs available. Monitoring for new events...
                        </div>
                    `;
                }

                const now = new Date();
                document.getElementById('last-updated').textContent = `Last updated: ${now.toLocaleString()}`;
                updateConnectionStatus(true);

            } catch (error) {
                console.error('Failed to refresh logs:', error);
                updateConnectionStatus(false);
                addLogEntry('error', `Failed to refresh logs: ${error.message}`);
            }
        }

        function addLogEntry(level, message) {
            const logsContent = document.getElementById('logs-content');
            const timestamp = new Date().toLocaleString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level ${level}">${level.toUpperCase()}</span>
                ${message}
            `;
            
            // Add to top of logs
            logsContent.insertBefore(logEntry, logsContent.firstChild);
            
            // Keep only last 50 entries
            while (logsContent.children.length > 50) {
                logsContent.removeChild(logsContent.lastChild);
            }
        }

        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connection-status');
            const statusText = document.getElementById('connection-text');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = 'Connected';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Disconnected';
            }
        }

        async function exportLogs() {
            try {
                addLogEntry('info', 'Exporting logs to Google Sheets...');
                
                // In real implementation, this would call your API to export logs
                const response = await fetch(`${API_BASE}/api/export-logs`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    addLogEntry('success', 'Logs exported to Google Sheets successfully');
                } else {
                    throw new Error('Export failed');
                }
            } catch (error) {
                addLogEntry('error', `Failed to export logs: ${error.message}`);
            }
        }

        function clearLogs() {
            const logsContent = document.getElementById('logs-content');
            logsContent.innerHTML = `
                <div class="log-entry info">
                    <span class="log-timestamp">[${new Date().toLocaleString()}]</span>
                    <span class="log-level info">INFO</span>
                    Logs display cleared. Monitoring continues...
                </div>
            `;
            addLogEntry('info', 'Log display cleared by user');
        }

        async function testReturn() {
            try {
                addLogEntry('info', 'Triggering test return processing...');

                const response = await fetch(`${API_BASE}/api/test-return`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ type: 'full' })
                });

                if (response.ok) {
                    addLogEntry('success', 'Test return processing started');
                } else {
                    throw new Error('Test return failed');
                }
            } catch (error) {
                addLogEntry('error', `Failed to trigger test return: ${error.message}`);
            }
        }

        async function testError() {
            try {
                const response = await fetch(`${API_BASE}/api/test-error`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    addLogEntry('info', 'Test error log added');
                } else {
                    throw new Error('Test error failed');
                }
            } catch (error) {
                addLogEntry('error', `Failed to trigger test error: ${error.message}`);
            }
        }

        // Add some initial sample logs
        setTimeout(() => {
            addLogEntry('success', 'Return processing service started successfully');
            addLogEntry('info', 'Monitoring Return Prime webhooks...');
            addLogEntry('info', 'Google Sheets integration active');
        }, 1000);
    </script>
</body>
</html>
