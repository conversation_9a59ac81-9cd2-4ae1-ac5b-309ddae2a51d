# Email Configuration Fix

## 🚨 Issue Identified

The error `SSL routines:ssl3_get_record:wrong version number` was caused by **incorrect TLS/SSL configuration** in the email transporters.

## ✅ Fix Applied

Updated all email service transporters with correct configuration:

### Before (Problematic):
```javascript
nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: false,
  requireTLS: true,
  tls: {
    ciphers: 'SSLv3',  // ❌ This was causing the issue
    rejectUnauthorized: false
  }
});
```

### After (Fixed):
```javascript
nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.port === 465, // ✅ Correct SSL detection
  auth: {
    user: config.email.user,
    pass: config.email.pass
  },
  tls: {
    rejectUnauthorized: false  // ✅ Simplified TLS config
  }
});
```

## 📝 Files Updated

1. `src/services/EmailService.ts`
2. `src/services/SupplierNotificationService.ts`
3. `src/services/CustomerNotificationService.ts`
4. `src/services/EnhancedReturnPrimeService.ts`

## 🔧 Environment Variables

Make sure your `.env` file has correct email settings:

### For Gmail SMTP:
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

### For Gmail SSL (Alternative):
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

### For Other SMTP Providers:
```env
EMAIL_HOST=your-smtp-host.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your-username
EMAIL_PASS=your-password
EMAIL_FROM=<EMAIL>
```

## 🎯 Key Changes

1. **Removed problematic SSL cipher specification**
2. **Auto-detect SSL based on port** (465 = SSL, others = STARTTLS)
3. **Simplified TLS configuration**
4. **Consistent configuration across all services**

## 🧪 Testing

After the fix, the system should:

1. ✅ **Connect to SMTP server** without SSL errors
2. ✅ **Send supplier emails** to development/production addresses
3. ✅ **Send customer emails** to actual customer addresses
4. ✅ **Send support copies** to <EMAIL>

## 📊 Expected Log Output

### Success Logs:
```
info: Development mode: Sending supplier notification to support emails
info: Supplier notification sent successfully
info: Customer notification sent successfully
info: Return processing completed
```

### No More SSL Errors:
- ❌ `SSL routines:ssl3_get_record:wrong version number`
- ✅ Emails should send successfully

## 🔄 Next Steps

1. **Restart the application** to apply the email configuration changes
2. **Test with a webhook** to verify emails are sent correctly
3. **Check logs** for successful email delivery
4. **Verify email routing** based on NODE_ENV setting

## 📧 Email Routing Summary

### Development Mode:
- **Supplier emails** → `<EMAIL>` + `<EMAIL>`
- **Customer emails** → Actual customer email + copy to `<EMAIL>`

### Production Mode:
- **Supplier emails** → Actual vendor database emails (e.g., `<EMAIL>`)
- **Customer emails** → Actual customer email + copy to `<EMAIL>`

The email configuration fix should resolve the SSL/TLS connection issues and allow emails to be sent successfully!
