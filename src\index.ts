import app from './app';
import { config } from './config';
import logger from './utils/logger';
import { ReturnProcessor } from './services/ReturnProcessor';
// COMMENTED OUT: MongoDB service - using vendor database JSON instead
// import { DatabaseService } from './services/DatabaseService';

// Create logs directory if it doesn't exist
import fs from 'fs';
if (!fs.existsSync('logs')) {
  fs.mkdirSync('logs');
}

async function startServer() {
  try {
    // COMMENTED OUT: MongoDB connection - using vendor database JSON instead
    // logger.info('Initializing MongoDB connection...');
    // const dbService = DatabaseService.getInstance();

    // try {
    //   await dbService.connect();
    //   await dbService.createIndexes();
    //   logger.info('✅ MongoDB connection established successfully');
    // } catch (dbError: any) {
    //   logger.error('❌ MongoDB connection failed', { error: dbError.message });
    //   if (config.nodeEnv === 'production') {
    //     logger.error('MongoDB is required in production mode. Exiting...');
    //     process.exit(1);
    //   } else {
    //     logger.warn('⚠️  Continuing without MongoDB in development mode. Admin dashboard will not work.');
    //   }
    // }

    logger.info('✅ Using vendor database JSON file instead of MongoDB');

    // Validate configuration on startup
    logger.info('Validating system configuration...');
    const processor = new ReturnProcessor();
    const validation = await processor.validateConfiguration();

    if (!validation.isValid) {
      logger.error('Configuration validation failed', { errors: validation.errors });
      logger.warn('Server starting with configuration issues. Some features may not work correctly.');
    } else {
      logger.info('Configuration validation successful');
    }

    // Start the server
    const server = app.listen(config.port, () => {
      logger.info('Server started successfully', {
        port: config.port,
        nodeEnv: config.nodeEnv,
        shopifyStore: config.shopify.storeUrl
      });
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);
      
      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });

      // Force close after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
}

startServer();
