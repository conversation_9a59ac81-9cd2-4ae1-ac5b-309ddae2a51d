# MongoDB Setup Guide for Supplier Admin Dashboard

## 🎯 Overview

This guide will help you set up MongoDB for the supplier admin dashboard system. You have several options depending on your preference.

## 📋 Option 1: MongoDB Atlas (Cloud - Recommended)

### Step 1: Create MongoDB Atlas Account
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Sign up for a free account
3. Create a new cluster (free tier is sufficient)

### Step 2: Configure Database Access
1. In Atlas dashboard, go to "Database Access"
2. Click "Add New Database User"
3. Create a user with username/password
4. <PERSON> "Read and write to any database" permissions

### Step 3: Configure Network Access
1. Go to "Network Access"
2. Click "Add IP Address"
3. Add your current IP or use `0.0.0.0/0` for development (not recommended for production)

### Step 4: Get Connection String
1. Go to "Clusters" and click "Connect"
2. Choose "Connect your application"
3. Copy the connection string (looks like: `mongodb+srv://username:<EMAIL>/`)

### Step 5: Update .env File
```env
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/
MONGODB_DB_NAME=shopify_automation
```

## 📋 Option 2: Local MongoDB Installation

### Windows Installation
1. Download MongoDB Community Server from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
2. Run the installer and follow the setup wizard
3. Install as a Windows Service
4. MongoDB Compass (GUI) is optional but recommended

### Start MongoDB Service
```powershell
# Start MongoDB service
net start MongoDB

# Or if installed manually
mongod --dbpath "C:\data\db"
```

### Update .env File
```env
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=shopify_automation
```

## 📋 Option 3: Docker MongoDB (Quick Setup)

### Step 1: Install Docker
Download and install Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)

### Step 2: Run MongoDB Container
```bash
# Create and run MongoDB container
docker run -d \
  --name mongodb-supplier \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password123 \
  -v mongodb_data:/data/db \
  mongo:latest

# For Windows PowerShell:
docker run -d --name mongodb-supplier -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password123 -v mongodb_data:/data/db mongo:latest
```

### Step 3: Update .env File
```env
MONGODB_URI=*******************************************
MONGODB_DB_NAME=shopify_automation
```

## 🚀 Setup and Migration

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Configure Environment
1. Copy `.env.example` to `.env`
2. Update MongoDB settings based on your chosen option above
3. Configure other required environment variables

### Step 3: Run Migration
```bash
# Migrate existing vendor data to MongoDB
node migrate-vendors-to-mongodb.js
```

### Step 4: Start the Server
```bash
# Development mode
npm run dev

# Production mode
npm run build
npm start
```

### Step 5: Access Admin Dashboard
Open your browser and go to: `http://localhost:3000/dashboard`

## 🔧 Troubleshooting

### Authentication Failed Error
If you get "bad auth : Authentication failed":

1. **Check credentials**: Ensure username/password are correct
2. **Check connection string**: Verify the MongoDB URI format
3. **Network access**: Ensure your IP is whitelisted (Atlas) or MongoDB is running (local)

### Connection Timeout
1. **Firewall**: Check if port 27017 is blocked
2. **Service status**: Ensure MongoDB service is running
3. **Network**: Verify network connectivity

### Sample .env Configuration
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration (Choose one option)
# Option 1: MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/
# Option 2: Local MongoDB
# MONGODB_URI=mongodb://localhost:27017
# Option 3: Docker MongoDB
# MONGODB_URI=*******************************************

MONGODB_DB_NAME=shopify_automation

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Return Prime Configuration
RETURN_PRIME_API_URL=https://api.returnprime.com/v1
RETURN_PRIME_ADMIN_ACCESS_TOKEN=de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4
RETURN_PRIME_WEBHOOK_SECRET=your_webhook_secret_here

# Shopify Configuration
SHOPIFY_STORE_URL=your-store.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_shopify_access_token
SHOPIFY_API_VERSION=2025-07

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

## 🎯 Quick Test

After setup, test the system:

```bash
# Test MongoDB connection
node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URI + '/' + process.env.MONGODB_DB_NAME)
  .then(() => { console.log('✅ MongoDB connected successfully'); process.exit(0); })
  .catch(err => { console.error('❌ MongoDB connection failed:', err.message); process.exit(1); });
"
```

## 📊 Features

Once set up, you'll have:

1. **Admin Dashboard**: Web interface to manage suppliers
2. **Real-time Webhook Processing**: Automatic supplier lookup from MongoDB
3. **Supplier Management**: Add, edit, delete suppliers with contact info
4. **Tag-based Matching**: Smart supplier matching using product tags
5. **Vendor Name Mapping**: Multiple vendor name variations per supplier
6. **Statistics Dashboard**: Real-time supplier statistics

## 🔄 Webhook Integration

The system automatically:
1. Receives Return Prime webhooks
2. Extracts vendor name and product tags
3. Queries MongoDB for matching suppliers
4. Falls back to JSON database if needed
5. Sends email notifications to correct suppliers

## 🎉 Success!

Once everything is set up, you can:
- Access the admin dashboard at `http://localhost:3000/dashboard`
- Add/edit suppliers through the web interface
- Process webhooks with real-time supplier lookup
- Monitor system health and statistics

Need help? Check the logs or contact support!
