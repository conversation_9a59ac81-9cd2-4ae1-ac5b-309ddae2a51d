# 🏪 Supplier Admin Dashboard Setup Guide

## ✅ Current Status

Your dashboard is now running at: **http://localhost:3000/dashboard**

## 🎯 What's Working

1. **✅ Server Running**: Express server on port 3000
2. **✅ Dashboard UI**: Admin interface with supplier management
3. **✅ API Endpoints**: RESTful API for supplier operations
4. **✅ JSON Fallback**: Works without MongoDB using existing vendor data
5. **✅ Search with Debouncing**: 300ms debounced search functionality
6. **✅ Real-time Stats**: Supplier statistics dashboard

## 🔧 Features Available

### Dashboard Features
- **Supplier Management**: Add, edit, delete suppliers
- **Real-time Search**: Debounced search across name, email, vendor names, and tags
- **Statistics**: Live supplier counts and priority breakdown
- **Responsive Design**: Works on desktop and mobile
- **Error Handling**: Graceful fallbacks and user-friendly error messages

### API Endpoints
- `GET /admin/suppliers` - List all suppliers
- `POST /admin/suppliers` - Create new supplier
- `PUT /admin/suppliers/:id` - Update supplier
- `DELETE /admin/suppliers/:id` - Delete supplier
- `GET /admin/stats/suppliers` - Get supplier statistics
- `GET /admin/test` - Test API connectivity

### Webhook Integration
- **Smart Supplier Lookup**: Matches webhook vendor names to suppliers
- **Tag-based Matching**: Uses product tags when vendor name is unclear
- **Multiple Name Support**: Handles various vendor name variations
- **Real-time Processing**: Instant supplier notifications

## 🧪 Testing the Dashboard

1. **Open Dashboard**: http://localhost:3000/dashboard
2. **Click "Test API"**: Verify all endpoints are working
3. **Check Console**: Open browser dev tools to see detailed logs
4. **Test Search**: Type in the search box (debounced)
5. **Add Supplier**: Click "Add New Supplier" to test form

## 📊 Current Data

The system currently has **2 suppliers** from the JSON database:
- **Smoke Drop** (HIGH priority, verified)
- **Buddify** (HIGH priority, verified)

## 🔄 MongoDB Integration (Optional)

### Current Status
- **MongoDB**: Not connected (using JSON fallback)
- **Fallback**: Fully functional with existing vendor-database.json
- **Migration**: Available when MongoDB is ready

### To Enable MongoDB
1. **Fix Connection**: Update MongoDB URI in `.env`
2. **Run Migration**: `node migrate-vendors-to-mongodb.js`
3. **Restart Server**: Dashboard will automatically use MongoDB

## 🚀 Usage Instructions

### Adding Suppliers
1. Click "➕ Add New Supplier"
2. Fill in required fields:
   - **Name**: Supplier business name
   - **Email**: Contact email for return notifications
   - **Priority**: HIGH/MEDIUM/LOW
   - **Shopify Vendor Names**: How they appear in Shopify (comma-separated)
   - **Tags**: Product categories/tags for matching

### Webhook Processing
The system automatically:
1. **Receives** Return Prime webhooks
2. **Extracts** vendor name and product info
3. **Matches** to suppliers using name/tags
4. **Sends** email notifications to correct supplier
5. **Logs** all processing for debugging

### Search Functionality
- **Debounced**: 300ms delay to prevent excessive API calls
- **Multi-field**: Searches name, email, vendor names, tags
- **Real-time**: Updates table as you type
- **Minimum Length**: Requires 2+ characters

## 🛠️ Troubleshooting

### Dashboard Not Loading
1. Check server is running: `npm run dev`
2. Verify port 3000 is accessible
3. Check browser console for errors
4. Click "Test API" button for diagnostics

### No Suppliers Showing
1. Check `vendor-database.json` exists
2. Click "🔄 Refresh Data"
3. Check browser console for API errors
4. Verify JSON file format is valid

### Search Not Working
1. Type at least 2 characters
2. Wait for 300ms debounce delay
3. Check browser console for errors
4. Try refreshing the page

## 📝 Next Steps

1. **Test Dashboard**: Verify all features work
2. **Add Suppliers**: Use the form to add your suppliers
3. **Test Webhooks**: Send test webhook to verify processing
4. **Monitor Logs**: Check server logs for webhook processing
5. **Setup MongoDB**: When ready for production database

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Dashboard loads at http://localhost:3000/dashboard
- ✅ Statistics show correct numbers
- ✅ Suppliers table displays data
- ✅ Search works with debouncing
- ✅ "Test API" button shows all green
- ✅ Can add/edit suppliers through the form

## 🔗 Important URLs

- **Dashboard**: http://localhost:3000/dashboard
- **Health Check**: http://localhost:3000/health
- **API Test**: http://localhost:3000/admin/test
- **Webhook Endpoint**: http://localhost:3000/webhook/return-prime

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Check server logs in terminal
3. Use "Test API" button for diagnostics
4. Verify all files are saved and server restarted

The dashboard is now fully functional with debounced search, real-time API integration, and comprehensive supplier management!
