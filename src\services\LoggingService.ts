import { google } from 'googleapis';
import Airtable from 'airtable';
import { ReturnLogEntry, SupplierName } from '../types';
import { config } from '../config';
import logger from '../utils/logger';

export class LoggingService {
  private sheets: any;
  private airtable: any;
  private useGoogleSheets: boolean;
  private useAirtable: boolean;

  constructor() {
    this.useGoogleSheets = !!config.googleSheets;
    this.useAirtable = !!config.airtable;
    
    if (this.useGoogleSheets) {
      this.initializeGoogleSheets();
    }
    
    if (this.useAirtable) {
      this.initializeAirtable();
    }

    if (!this.useGoogleSheets && !this.useAirtable) {
      logger.warn('No logging service configured (Google Sheets or Airtable)');
    }
  }

  /**
   * Initialize Google Sheets API
   */
  private async initializeGoogleSheets(): Promise<void> {
    try {
      const auth = new google.auth.GoogleAuth({
        credentials: {
          client_email: config.googleSheets!.serviceAccountEmail,
          private_key: config.googleSheets!.privateKey
        },
        scopes: ['https://www.googleapis.com/auth/spreadsheets']
      });

      this.sheets = google.sheets({ version: 'v4', auth });
      logger.info('Google Sheets API initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Google Sheets API', { error });
      this.useGoogleSheets = false;
    }
  }

  /**
   * Initialize Airtable
   */
  private initializeAirtable(): void {
    try {
      Airtable.configure({
        apiKey: config.airtable!.apiKey
      });

      this.airtable = Airtable.base(config.airtable!.baseId);
      logger.info('Airtable initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Airtable', { error });
      this.useAirtable = false;
    }
  }

  /**
   * Log return entry to configured services
   */
  public async logReturnEntry(entry: ReturnLogEntry): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    let anySuccess = false;

    // If no logging service is configured, just log to console and return success
    if (!this.useGoogleSheets && !this.useAirtable) {
      console.log('📝 RETURN LOG ENTRY (No external logging configured):', JSON.stringify(entry, null, 2));
      logger.info('Return entry logged to console only', { returnId: entry.return_id });
      return { success: true, errors: [] };
    }

    // Log to Google Sheets
    if (this.useGoogleSheets) {
      try {
        await this.logToGoogleSheets(entry);
        anySuccess = true;
        logger.debug('Return entry logged to Google Sheets', { returnId: entry.return_id });
      } catch (error) {
        const errorMsg = `Google Sheets logging failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        logger.error('Failed to log to Google Sheets', { error, entry });
      }
    }

    // Log to Airtable
    if (this.useAirtable) {
      try {
        await this.logToAirtable(entry);
        anySuccess = true;
        logger.debug('Return entry logged to Airtable', { returnId: entry.return_id });
      } catch (error) {
        const errorMsg = `Airtable logging failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        logger.error('Failed to log to Airtable', { error, entry });
      }
    }

    return {
      success: anySuccess,
      errors
    };
  }

  /**
   * Log entry to Google Sheets
   */
  private async logToGoogleSheets(entry: ReturnLogEntry): Promise<void> {
    if (!this.sheets || !config.googleSheets) {
      throw new Error('Google Sheets not configured');
    }

    // Ensure headers exist
    await this.ensureGoogleSheetsHeaders();

    const values = [
      entry.timestamp,
      entry.return_id,
      entry.order_id,
      entry.sku,
      entry.supplier,
      entry.qty.toString(),
      entry.status,
      entry.customer_email,
      entry.reason,
      entry.event_type,
      entry.processed_at,
      entry.error || ''
    ];

    await this.sheets.spreadsheets.values.append({
      spreadsheetId: config.googleSheets.sheetsId,
      range: 'Returns!A:L',
      valueInputOption: 'USER_ENTERED',
      resource: {
        values: [values]
      }
    });
  }

  /**
   * Ensure Google Sheets has proper headers
   */
  private async ensureGoogleSheetsHeaders(): Promise<void> {
    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: config.googleSheets!.sheetsId,
        range: 'Returns!A1:L1'
      });

      if (!response.data.values || response.data.values.length === 0) {
        // Add headers
        const headers = [
          'Timestamp',
          'Return ID',
          'Order ID',
          'SKU',
          'Supplier',
          'Quantity',
          'Status',
          'Customer Email',
          'Reason',
          'Event Type',
          'Processed At',
          'Error'
        ];

        await this.sheets.spreadsheets.values.update({
          spreadsheetId: config.googleSheets!.sheetsId,
          range: 'Returns!A1:L1',
          valueInputOption: 'USER_ENTERED',
          resource: {
            values: [headers]
          }
        });

        logger.info('Added headers to Google Sheets');
      }
    } catch (error) {
      logger.warn('Could not check/add headers to Google Sheets', { error });
    }
  }

  /**
   * Log entry to Airtable
   */
  private async logToAirtable(entry: ReturnLogEntry): Promise<void> {
    if (!this.airtable || !config.airtable) {
      throw new Error('Airtable not configured');
    }

    const record = {
      'Timestamp': entry.timestamp,
      'Return ID': entry.return_id,
      'Order ID': entry.order_id,
      'SKU': entry.sku,
      'Supplier': entry.supplier,
      'Quantity': entry.qty,
      'Status': entry.status,
      'Customer Email': entry.customer_email,
      'Reason': entry.reason,
      'Event Type': entry.event_type,
      'Processed At': entry.processed_at,
      'Error': entry.error || ''
    };

    await new Promise((resolve, reject) => {
      this.airtable(config.airtable!.tableName).create([
        { fields: record }
      ], (err: any, records: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(records);
        }
      });
    });
  }

  /**
   * Log multiple return entries in batch
   */
  public async logReturnEntries(entries: ReturnLogEntry[]): Promise<{ success: boolean; errors: string[] }> {
    const allErrors: string[] = [];
    let anySuccess = false;

    for (const entry of entries) {
      const result = await this.logReturnEntry(entry);
      if (result.success) {
        anySuccess = true;
      }
      allErrors.push(...result.errors);
    }

    return {
      success: anySuccess,
      errors: allErrors
    };
  }

  /**
   * Create return log entry from return data
   */
  public createReturnLogEntry(
    returnId: string,
    orderId: string,
    sku: string,
    supplier: SupplierName,
    qty: number,
    status: string,
    customerEmail: string,
    reason: string,
    eventType: string,
    error?: string
  ): ReturnLogEntry {
    const now = new Date().toISOString();
    
    return {
      timestamp: now,
      return_id: returnId,
      order_id: orderId,
      sku,
      supplier,
      qty,
      status,
      customer_email: customerEmail,
      reason,
      event_type: eventType,
      processed_at: now,
      error
    };
  }

  /**
   * Test logging service configuration
   */
  public async testConfiguration(): Promise<{ googleSheets: boolean; airtable: boolean }> {
    const results = {
      googleSheets: false,
      airtable: false
    };

    // Test Google Sheets
    if (this.useGoogleSheets && this.sheets) {
      try {
        await this.sheets.spreadsheets.get({
          spreadsheetId: config.googleSheets!.sheetsId
        });
        results.googleSheets = true;
        logger.info('Google Sheets configuration test successful');
      } catch (error) {
        logger.error('Google Sheets configuration test failed', { error });
      }
    }

    // Test Airtable
    if (this.useAirtable && this.airtable) {
      try {
        await new Promise((resolve, reject) => {
          this.airtable(config.airtable!.tableName).select({
            maxRecords: 1
          }).firstPage((err: any, records: any) => {
            if (err) {
              reject(err);
            } else {
              resolve(records);
            }
          });
        });
        results.airtable = true;
        logger.info('Airtable configuration test successful');
      } catch (error) {
        logger.error('Airtable configuration test failed', { error });
      }
    }

    return results;
  }
}
