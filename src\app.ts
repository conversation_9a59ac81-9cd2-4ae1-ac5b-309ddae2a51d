import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import { config } from './config';
import webhookRoutes from './routes/webhook';
import adminRoutes from './routes/admin';
// import shopifyWebhookRoutes from './routes/shopifyWebhook';
// import productLookupRoutes from './routes/productLookup';
import { ReturnProcessor } from './services/ReturnProcessor';
import { ShopifyWebhookService } from './services/ShopifyWebhookService';
import { DatabaseService } from './services/DatabaseService';
import logger from './utils/logger';

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration - allow all origins
app.use(cors({
  origin: true,
  credentials: true
}));

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Request logging middleware
app.use((req, res, next) => {
  logger.info('Incoming request', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Routes
app.use('/', webhookRoutes);
app.use('/admin', adminRoutes);

// Serve admin dashboard
app.get('/dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, '../admin-dashboard.html'));
});

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'Shopify Return Automation',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Detailed health check endpoint
app.get('/health', async (req, res) => {
  const dbService = DatabaseService.getInstance();
  const dbHealth = await dbService.healthCheck();

  res.json({
    service: 'Shopify Return Automation',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    database: {
      status: dbHealth.status,
      connected: dbHealth.details.connected,
      responseTime: dbHealth.details.responseTime
    },
    endpoints: {
      dashboard: '/dashboard',
      admin: '/admin',
      webhook: '/webhook/return-prime'
    }
  });
});

// Configuration validation endpoint
app.get('/config/validate', async (req, res) => {
  try {
    const processor = new ReturnProcessor();
    const validation = await processor.validateConfiguration();

    res.json({
      isValid: validation.isValid,
      errors: validation.errors,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Configuration validation failed', { error });
    res.status(500).json({
      isValid: false,
      errors: ['Configuration validation failed'],
      timestamp: new Date().toISOString()
    });
  }
});

// Shopify webhook management endpoints
app.post('/shopify/webhooks/setup', async (req, res) => {
  try {
    const { webhookEndpoint } = req.body;

    if (!webhookEndpoint) {
      return res.status(400).json({
        success: false,
        error: 'webhookEndpoint is required'
      });
    }

    const shopifyWebhookService = new ShopifyWebhookService();
    const result = await shopifyWebhookService.setupWebhooks(webhookEndpoint);

    return res.json(result);
  } catch (error) {
    logger.error('Failed to setup Shopify webhooks', { error });
    return res.status(500).json({
      success: false,
      error: 'Failed to setup webhooks'
    });
  }
});

app.get('/shopify/webhooks/list', async (req, res) => {
  try {
    const shopifyWebhookService = new ShopifyWebhookService();
    const result = await shopifyWebhookService.listWebhooks();

    res.json(result);
  } catch (error) {
    logger.error('Failed to list Shopify webhooks', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to list webhooks'
    });
  }
});

app.delete('/shopify/webhooks/:webhookId', async (req, res) => {
  try {
    const { webhookId } = req.params;

    const shopifyWebhookService = new ShopifyWebhookService();
    const result = await shopifyWebhookService.deleteWebhook(webhookId);

    res.json(result);
  } catch (error) {
    logger.error('Failed to delete Shopify webhook', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to delete webhook'
    });
  }
});

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(500).json({
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  logger.warn('Route not found', {
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(404).json({
    error: 'Route not found',
    timestamp: new Date().toISOString()
  });
});

export default app;
